﻿@using Consumer.Resources.Mobile.Cardapio.Views.Login
@model RAL.Consumer.Mobile.Areas.Cardapio.Models.LoginOpcionalViewModel
@using Microsoft.Owin.Security
@{
    ViewBag.Title = @OpcionalRes.Cardapio;
}

<div class="row">
    <div class="col-md-6 col-md-offset-3">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4>
                    @OpcionalRes.InformeDados
                </h4>
                <p>@OpcionalRes.DesejaCadastrar</p>
            </div>
            <div class="panel-body">
                @using (Html.BeginForm("Opcional", "Login", FormMethod.Post, new { id = "formLogin" }))
                {
                    @Html.AntiForgeryToken()
                    <div class="row">
                        <div class="col-xs-12">
                            <div class="form-group">
                                @Html.EditorFor(model => model.Nome, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = Html.DisplayNameFor(n => n.Nome) } })
                                @Html.ValidationMessageFor(model => model.Nome, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="form-group">
                                @Html.EditorFor(model => model.Email, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = Html.DisplayNameFor(n => n.Email) } })
                                @Html.ValidationMessageFor(model => model.Email, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="col-xs-12">
                            <div class="form-group">
                                @Html.EditorFor(model => model.Telefone, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = Html.DisplayNameFor(n => n.Telefone), data_masked = "", data_inputmask = "'mask': ['(99) 9999-9999', '(99) 99999-9999']" } })
                                @Html.ValidationMessageFor(model => model.Telefone, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="col-xs-8">
                            <div class="form-group">
                                @Html.EditorFor(model => model.Cep, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = Html.DisplayNameFor(n => n.Cep), data_masked = "", data_inputmask = "'mask': '99999-999'" } })
                                @Html.ValidationMessageFor(model => model.Cep, "", new { @class = "text-danger" })
                            </div>
                        </div>
                        <div class="col-xs-4">
                            <div class="form-group">
                                @Html.EditorFor(model => model.Numero, new { htmlAttributes = new { @class = "form-control input-lg", placeholder = HttpUtility.HtmlDecode(Html.DisplayNameFor(n => n.Numero).ToHtmlString()) } })
                                @Html.ValidationMessageFor(model => model.Numero, "", new { @class = "text-danger" })
                            </div>
                        </div>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@section BodyArea {
    <nav class="navbar navbar-default navbar-fixed-bottom" role="navigation">
        <div class="col-xs-4">
            <button id="btnChamarGarcom" class="btn btn-lg btn-block btn-square btn-warning navbar-btn" title="@OpcionalRes.ChamarGarcom"><i class="fa fa-bell"></i><br /><span class="hidden-xs">@OpcionalRes.Chamar</span>@OpcionalRes.Garcom</button>
        </div>
        <div class="col-xs-8">
            <button id="btn-continuar" type="submit" class="btn btn-lg btn-block btn-square btn-success navbar-btn"><i class="fa fa-sign-in"></i><br />@OpcionalRes.VerCardapio</button>
        </div>
    </nav>
}

@section Scripts{
    <script>
        $(document).ready(function () {
            $(window).unload(saveData);
            loadData();
            $("#btn-continuar").click(function () {
                $("#formLogin").submit();
            });
            $(document).on("click", "#btnChamarGarcom", function (e) {
                chamarGarcom(e);
            });
        });

        function loadData() {
            if (typeof localStorage !== "undefined") {
                $('#Nome').val(localStorage.nome);
                $('#Email').val(localStorage.email);
                $('#Telefone').val(localStorage.telefone);
                $('#Cep').val(localStorage.cep);
                $('#Numero').val(localStorage.numero);
            }
        }

        function saveData() {
            localStorage.nome = $('#Nome').val();
            localStorage.email = $('#Email').val();
            localStorage.telefone = $('#Telefone').val();
            localStorage.cep = $("#Cep").val();
            localStorage.numero = $('#Numero').val();
        }
    </script>
}